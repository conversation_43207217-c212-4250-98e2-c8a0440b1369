/* 引入访客公共样式 */
@import "/styles/visitor-common.wxss";

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  overflow: hidden;
}

/* 筛选区域 */
.filter-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 99;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 120px;
}

.status-tabs {
  display: flex;
  white-space: nowrap;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
}

.status-tab {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 8px;
  font-size: 14px;
  color: #666666;
  border-radius: 16px;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.status-tab.active {
  background-color: #FF8C00;
  color: #fff;
  border-color: #FF8C00;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #f5f7fa;
  margin: 8px 16px 12px;
  border-radius: 8px;
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  height: 32px;
  font-size: 14px;
}

.clear-btn {
  padding: 4px;
}

.clear-icon {
  width: 16px;
  height: 16px;
}

/* 访客列表 */
.visitor-list {
  margin-top: 120px;
  padding: 8px 12px 0;
  box-sizing: border-box;
  height: calc(100% - 220rpx);
  margin-bottom: 115rpx;
  padding-bottom: 40rpx;
}

/* 分组样式 */
.visitor-group {
  margin-bottom: 16px;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f9fafb;
  border-radius: 8px 8px 0 0;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.group-count {
  font-size: 12px;
  color: #999999;
}

.visitor-cards {
  background-color: transparent;
}

/* 访客卡片 */
.visitor-card {
  position: relative;
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  width: calc(100% - 4px);
  box-sizing: border-box;
  overflow: hidden;
  border: 1px solid #E0E0E0;
}

.visitor-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.visitor-info-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.visitor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  margin-right: 12px;
}

.visitor-avatar.pending, .visitor-avatar.wait_visit {
  background-color: #FAAD14;
}

.visitor-avatar.visited, .visitor-avatar.completed {
  background-color: #52C41A;
}

.visitor-avatar.expired {
  background-color: #999999;
}

.visitor-avatar.canceled {
  background-color: #F5222D;
}

.visitor-basic-info {
  flex: 1;
  min-width: 0;
}

.visitor-name {
  font-size: 17px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.visitor-time {
  font-size: 12px;
  color: #999999;
}

.visitor-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 80px;
}

.status-tag {
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 4px;
  white-space: nowrap;
  font-weight: 500;
}

.status-pending, .status-wait_visit {
  background-color: #FFF7E6;
  color: #FAAD14;
}

.status-visited, .status-completed {
  background-color: #F6FFED;
  color: #52C41A;
}

.status-expired {
  background-color: #F5F5F5;
  color: #999999;
}

.status-canceled {
  background-color: #FFF1F0;
  color: #F5222D;
}

/* 访客卡片内容 */
.visitor-card-body {
  margin-bottom: 12px;
}

.visitor-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.visitor-end-time, .visitor-note, .visitor-vehicle {
  font-size: 12px;
  color: #666666;
}

/* 访客卡片操作 */
.visitor-card-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
  padding-top: 12px;
  gap: 8px;
}

.action-btn {
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 500;
  transition: all 0.3s;
  border: none;
  line-height: 1.4;
  min-width: 0;
  max-width: fit-content;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.view-btn {
  background-color: #FFFFFF;
  color: #FF8C00;
  border: 1px solid #FF8C00;
}

.share-btn {
  background-color: #FFFFFF;
  color: #1890ff;
  border: 1px solid #1890ff;
}

.extend-btn {
  background-color: #FF8C00;
  color: #FFFFFF;
}

.delete-btn {
  background-color: #FFFFFF;
  color: #F5222D;
  border: 1px solid #F5222D;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #eee;
  border-top-color: #FF8C00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 8px;
}

/* 空状态提示 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 0;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 24px;
}

.create-btn {
  background-color: #FF8C00;
  color: #fff;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: background-color 0.3s;
  border: none;
}

/* 分页指示器 */
.pagination {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #999;
}

.load-more .loading-spinner {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

/* 悬浮添加按钮 */
.add-btn {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 56px;
  height: 56px;
  background-color: #FF8C00;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 140, 0, 0.3);
  z-index: 100;
}

.add-icon {
  width: 24px;
  height: 24px;
}

/* 延期弹窗样式 */
.extend-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.extend-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background-color: #fff;
  border-radius: 12px;
  margin: 0 20px;
  max-width: 400px;
  width: 100%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  padding: 4px;
}

.close-icon {
  width: 20px;
  height: 20px;
}

.modal-body {
  padding: 0;
}

.extend-option {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  font-size: 16px;
  color: #333;
  transition: background-color 0.2s;
}

.extend-option:last-child {
  border-bottom: none;
}

.extend-option:active {
  background-color: #f5f5f5;
}
