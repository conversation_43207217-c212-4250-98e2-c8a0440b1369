// community-select.js
const commApi = require('../../api/communityApi.js');

Page({
  data: {
    searchKeyword: '',
    allList: [],
    filteredList: [],
    loading: true,
    userLocation: null,
    locationPermissionGranted: false
  },

  onLoad: function () {
    // 获取用户位置
    this.getUserLocation()

    // 加载小区列表
    this.loadCommunityList()
  },

  // 加载小区列表
  loadCommunityList: function () {
    this.setData({ loading: true })

    commApi.communityList({
      pageNum: 1,
      pageSize: 100 // 获取足够多的数据
    }).then(res => {
      console.log('小区列表数据：', res)

      if ( res && res.list) {
        const communityList = res.list.map(item => ({
          id: item.id,
          communityName: item.communityName,
          address: item.address || '',
          distance: '计算中...', // 后续根据用户位置计算
          lng: item.lng,
          lat: item.lat,
          orgId: item.orgId
        }))

        this.setData({
          allList: communityList,
          filteredList: communityList,
          loading: false
        })

        // 如果有用户位置，计算距离
        if (this.data.userLocation) {
          this.calculateDistances()
        } else {
          // 没有位置权限，使用默认排序
          this.sortByDefault()
        }
      } else {
        console.error('获取小区列表失败：', res)
        wx.showToast({
          title: '获取小区列表失败',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('获取小区列表异常：', err)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
      this.setData({ loading: false })
    })
  },

  // 获取用户位置
  getUserLocation: function () {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const latitude = res.latitude
        const longitude = res.longitude
        console.log('当前位置：', latitude, longitude)

        this.setData({
          userLocation: { latitude, longitude },
          locationPermissionGranted: true
        })

        // 如果已经加载了小区列表，计算距离
        if (this.data.allList.length > 0) {
          this.calculateDistances()
        }
      },
      fail: (err) => {
        console.log('获取位置失败：', err)
        this.setData({
          locationPermissionGranted: false
        })

        // 用户拒绝位置权限，使用默认排序
        if (this.data.allList.length > 0) {
          this.sortByDefault()
        }
      }
    })
  },

  // 计算距离
  calculateDistances: function () {
    if (!this.data.userLocation) return

    const userLat = this.data.userLocation.latitude
    const userLng = this.data.userLocation.longitude

    const updatedList = this.data.allList.map(item => {
      if (item.lat && item.lng) {
        const distance = this.getDistance(userLat, userLng, item.lat, item.lng)
        return {
          ...item,
          distance: distance < 1 ? `${Math.round(distance * 1000)}m` : `${distance.toFixed(1)}km`,
          distanceValue: distance // 用于排序的数值
        }
      }
      return {
        ...item,
        distance: '未知',
        distanceValue: 999999 // 未知距离排在最后
      }
    })

    // 按距离排序（从小到大）
    updatedList.sort((a, b) => a.distanceValue - b.distanceValue)

    this.setData({
      allList: updatedList,
      filteredList: this.data.searchKeyword ? this.filterList(updatedList, this.data.searchKeyword) : updatedList
    })
  },

  // 默认排序（按小区名称）
  sortByDefault: function () {
    const sortedList = [...this.data.allList].map(item => ({
      ...item,
      distance: '' // 不显示距离
    })).sort((a, b) => a.communityName.localeCompare(b.communityName))

    this.setData({
      allList: sortedList,
      filteredList: this.data.searchKeyword ? this.filterList(sortedList, this.data.searchKeyword) : sortedList
    })
  },

  // 计算两点间距离（公里）
  getDistance: function (lat1, lng1, lat2, lng2) {
    const radLat1 = lat1 * Math.PI / 180.0
    const radLat2 = lat2 * Math.PI / 180.0
    const a = radLat1 - radLat2
    const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0
    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
      Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)))
    s = s * 6378.137 // 地球半径
    s = Math.round(s * 10000) / 10000
    return s
  },

  // 搜索输入
  onSearchInput: function (e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })
    this.filterCommunities(keyword)
  },

  // 清除搜索
  clearSearch: function () {
    this.setData({
      searchKeyword: ''
    })
    this.filterCommunities('')
  },

  // 过滤小区列表
  filterCommunities: function (keyword) {
    if (!keyword) {
      this.setData({
        filteredList: this.data.allList
      })
      return
    }

    const filtered = this.filterList(this.data.allList, keyword)
    this.setData({
      filteredList: filtered
    })
  },

  // 过滤列表的通用方法
  filterList: function (list, keyword) {
    return list.filter(item => {
      return item.communityName.indexOf(keyword) !== -1 || item.address.indexOf(keyword) !== -1
    })
  },

  // 选择小区
  selectCommunity: function (e) {
    const community = e.currentTarget.dataset.community
   
    // 保存到本地存储
    wx.setStorageSync('selectedCommunity', community)


      wx.navigateBack({
        success: () => {
          // 通知上一页刷新
          const pages = getCurrentPages()
          const prevPage = pages[pages.length - 2]
          if (prevPage && prevPage.checkCommunitySelection) {
            prevPage.checkCommunitySelection()
          }
        }
      })
  
  }
})
