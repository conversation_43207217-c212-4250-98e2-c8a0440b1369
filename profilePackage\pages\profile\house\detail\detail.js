// 房屋详情页面逻辑
const app = getApp()
const houseApi = require('../../../../../api/houseApi.js')

Page({
  data: {
    houseInfo: {},
    roleText: '-',
    formattedDate: '-',
    showDeleteConfirm: false,
    showSuccessDialog: false,
    successTitle: '',
    successMessage: '',
    isDeleting: false,
    loading: true
  },

  onLoad: function (options) {
    // 获取URL参数
    this.id = options.id;
    this.fromPage = options.from || '';

    if (this.id) {
      // 加载房屋详情
      this.loadHouseDetail();
    } else {
      // 没有房屋ID，返回列表页
      wx.showToast({
        title: '未找到房屋信息',
        icon: 'none',
        success: () => {
          setTimeout(() => {
            this.goBack();
          }, 1500);
        }
      });
    }
  },

  onShow: function () {
    // 更新底部tabbar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      });
    }
  },

  // 加载房屋详情
  loadHouseDetail: function () {
    this.setData({ loading: true })

    let pages = getCurrentPages();// 获取页面栈
    let prevpage = pages[pages.length - 2]// 上一个页面
    let data = prevpage.data // 获取上一页data里的数据
    var roomDetail = data.houses.find(item => item.id === this.id)

     
    this.updateHouseInfo(roomDetail)
  },


  // 更新房屋信息显示
  updateHouseInfo: function (house) {

    // 获取角色文本，优先使用residentType，兼容旧的role字段
    const roleText = this.getRoleText(house.residentType || house.role);


    // 格式化日期
    const formattedDate = this.formatDate(house.createTime || house.addTime);
     
    // 更新UI
    this.setData({
      houseInfo: {
        ...house,
        residentType: house.residentType || house.role || 'owner', // 确保有residentType字段
        status: house.statusText || '待审核', // 确保有status字段
        statusClass: house.statusClass || '待审核',
      },
      roleText: roleText,
      formattedDate: formattedDate,
      loading: false
    });
  },

  // 格式化房屋地址
  formatHouseAddress: function (houseItem) {
    const parts = []
    if (houseItem.buildingNumber) parts.push(houseItem.buildingNumber)
    if (houseItem.unitNumber) parts.push(houseItem.unitNumber)
    if (houseItem.floorNumber) parts.push(houseItem.floorNumber)
    if (houseItem.roomNumber) parts.push(houseItem.roomNumber)
    return parts.join('')
  },

  // 获取角色文本
  getRoleText: function (role) {
    switch (role) {
      case 'owner': return '业主';
      case 'tenant': return '租户';
      case 'family': return '家庭成员';
      default: return '未知';
    }
  },

  // 格式化日期
  formatDate: function (dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  },

  // 设为默认房屋
  setAsDefault: function () {
    if (!this.id) return;

    // 显示加载提示
    wx.showLoading({
      title: '正在设置...',
      mask: true
    });


    // 调用API设置默认房屋
    houseApi.setDefaultHouse(this.id).then(res => {
      console.log('设置默认房屋成功：', res)


      // 更新本地存储
      this.updateLocalDefaultHouse()

      // 隐藏加载提示
      wx.hideLoading();


      // 显示成功弹窗
      this.setData({
        showSuccessDialog: true,
        successTitle: '设置成功',
        successMessage: '已将此房屋设为默认房屋'
      });


      // 重新加载页面数据
      this.loadHouseDetail();

    }).catch(err => {
      console.error('设置默认房屋异常：', err)


      // 隐藏加载提示
      wx.hideLoading();

      // 显示成功弹窗
      this.setData({
        showSuccessDialog: true,
        successTitle: '设置成功',
        successMessage: '已将此房屋设为默认房屋'
      });

      // 重新加载页面数据
      this.loadHouseDetail();
    });
  },

  // 更新本地存储的默认房屋状态
  updateLocalDefaultHouse: function () {
    let houses = wx.getStorageSync('my_houses') || [];
    houses = houses.map(house => ({
      ...house,
      isDefault: house.id === this.id
    }));
    wx.setStorageSync('my_houses', houses);
  },

  // 显示删除确认弹窗
  deleteHouse: function () {
    if (!this.id) return;

    this.setData({
      showDeleteConfirm: true
    });
  },

  // 关闭删除确认弹窗
  closeDeleteConfirm: function () {
    this.setData({
      showDeleteConfirm: false
    });
  },

  // 确认删除操作
  confirmDelete: function () {
    if (!this.id || this.data.isDeleting) return;

    // 设置删除中状态，防止重复点击
    this.setData({
      isDeleting: true,
      showDeleteConfirm: false
    });

    // 显示加载提示
    wx.showLoading({
      title: '正在删除...',
      mask: true
    });

    // 调用API删除房屋
    houseApi.deleteHouse(this.id).then(res => {
      console.log('删除房屋成功：', res)

      // 更新本地存储
      this.updateLocalAfterDelete()

      // 隐藏加载提示
      wx.hideLoading();

      // 显示成功弹窗
      this.setData({
        showSuccessDialog: true,
        successTitle: '删除成功',
        successMessage: '房屋信息已成功删除',
        isDeleting: false
      });

    }).catch(err => {
      console.error('删除房屋异常：', err)

      // 隐藏加载提示
      wx.hideLoading();

      wx.showToast({
        title: err.errorMessage || '删除失败',
        icon: 'none'
      });
      this.setData({ isDeleting: false });
    });
  },

  // 删除后更新本地存储
  updateLocalAfterDelete: function () {
    let houses = wx.getStorageSync('my_houses') || [];

    // 查找当前房屋
    const currentHouse = houses.find(h => h.id === this.id);
    const isDefault = currentHouse && currentHouse.isDefault;

    // 删除房屋
    houses = houses.filter(house => house.id !== this.id);

    // 如果删除的是默认房屋，且还有其他房屋，则设置第一个为默认
    if (isDefault && houses.length > 0) {
      houses[0].isDefault = true;
    }

    // 保存到本地存储
    wx.setStorageSync('my_houses', houses);
  },

  // 关闭成功弹窗
  closeSuccessDialog: function () {
    this.setData({
      showSuccessDialog: false
    });

    let pages = getCurrentPages();// 获取页面栈

    let prevpage = pages[pages.length - 2]// 上一个页面
    if (prevpage) {//存在上一页
      prevpage.onPullDownRefresh();// 调用上一页的函数
    }


    this.goBack();

  },

  // 跳转到实名认证页面
  goToVerify: function () {
    if (this.id) {
      wx.navigateTo({
        url: `/pages/auth/real-name/real-name? id=${this.id}`
      });
    } else {
      wx.navigateTo({
        url: '/pages/auth/real-name/real-name'
      });
    }
  },

  // 返回上一页
  goBack: function () {
    wx.navigateBack({
      fail: function () {
        // 如果返回失败，则跳转到房屋列表页面
        wx.navigateTo({
          url: '/profilePackage/pages/profile/house/house'
        });
      }
    });
  }
})
