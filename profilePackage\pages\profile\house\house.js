// 我的房屋页面逻辑
const app = getApp()
const houseApi = require('@/api/houseApi.js')
const util = require('@/utils/util.js')

Page({
  data: {
    houses: [],
    loading: false,

    // 分页相关数据
    pageNum: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,
    loadingMore: false,

    // 新增房屋弹窗相关数据
    showAddHouseModal: false,
    currentStep: 'building', // 'building' | 'unit' | 'room'
    searchKeyword: '',
    searchPlaceholder: '搜索楼栋',

    // 楼栋、单元和房间数据
    buildings: [],
    units: [],
    rooms: [],
    filteredBuildings: [],
    filteredUnits: [],
    filteredRooms: [],

    // 原始房间数据（用于分组处理）
    originalRooms: [],

    // 选择状态
    selectedBuildingId: null,
    selectedBuildingName: '',
    selectedUnitNumber: '',
    selectedRoomId: null,
    selectedRoomName: '',
    selectedResidentType: '',

    // 字典数据
    residentTypeOptions: [],

    // 提交状态
    canSubmit: false,
    isSubmitting: false,
    needRefresh: false,

    // 绑定住户弹窗相关数据
    showFindHouseModal: false,
    findHouseLoading: false,
    selectedResidentsCount: 0,
    bindingResidents: false,
    realPersonData: null // 实名认证数据（包含住户信息）
  },

  onLoad: function (options) {
    // 获取URL参数
    this.fromPage = options.from || '';
  },

  onShow: function () {
    console.log('onShow house')
    // 加载房屋列表
    this.loadHouses();
    // 加载字典数据
    this.loadResidentTypeDict();
  },


  // 加载房屋列表
  loadHouses: function (refresh = false) {
    // 如果是刷新，重置分页数据
    if (refresh) {
      this.setData({
        pageNum: 1,
        houses: [],
        hasMore: true,
        total: 0,
        loading: true
      });
    } else {
      // 检查是否还有更多数据
      if (!this.data.hasMore || this.data.loadingMore) {
        return;
      }
      this.setData({ loadingMore: true });
    }
    const selectedCommunity = wx.getStorageSync('selectedCommunity');

    const params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      communityId: selectedCommunity.id
    };

    console.log('加载房屋列表参数：', params);

    houseApi.getHouseList(params)
      .then(res => {
        console.log('房屋列表数据：', res);



        if (res) {

          if (res.list && res.list.length == 0) {
            console.log('房产列表为空，尝试获取实名认证房产信息');
            wx.showToast({
              title: '暂无房屋数据',
              icon: 'none'
            });

            this.setData({
              loading: false,
              loadingMore: false
            });

            this.checkRealPersonHouse();
            return;
          }

          const newHouseList = (res.list || []).map(item => ({
            id: item.id,
            roomId: item.roomId,
            buildingNumber: item.buildingNumber,
            unitNumber: item.unitNumber || '',
            floorNumber: item.floorNumber || '',
            roomNumber: item.roomNumber || '',
            fullAddress: this.formatHouseAddress(item),
            area: item.area || 0,
            residentType: item.residentType || 'owner',
            roleText: this.getResidentTypeText(item.residentType || 'owner'),
            isDefault: item.isDefault || false,
            status: item.status,
            isVerified: item.isVerified || false,
            createTime: item.createTime,
            updateTime: item.updateTime
          }));

          // 处理房屋数据
          const processedHouseList = newHouseList.map(house => {

            var sss = houseApi.formatHouseData(house);
            return sss
          });

          // 合并数据
          let allHouses = refresh ? processedHouseList : [...this.data.houses, ...processedHouseList];

          // 按默认房屋优先排序（仅在刷新时排序）
          if (refresh) {
            allHouses.sort((a, b) => {
              if (a.isDefault && !b.isDefault) return -1;
              if (!a.isDefault && b.isDefault) return 1;
              return 0;
            });
          }

          // 计算分页信息
          const total = res.total || 0;
          const currentTotal = allHouses.length;
          const hasMore = currentTotal < total;

          this.setData({
            houses: allHouses,
            total: total,
            hasMore: hasMore,
            pageNum: hasMore ? this.data.pageNum + 1 : this.data.pageNum,
            loading: false,
            loadingMore: false
          });

          console.log(`加载完成：当前${currentTotal}条，总共${total}条，还有更多：${hasMore}`);

        } else {

          wx.showToast({
            title: '暂无房屋数据',
            icon: 'none'
          });

          this.setData({
            loading: false,
            loadingMore: false
          });

          this.checkRealPersonHouse();
        }

      })
      .catch(err => {

        console.error('获取房屋列表异常：', err);

        this.setData({
          loading: false,
          loadingMore: false
        });
      });
  },


  // 格式化房屋地址
  formatHouseAddress: function (houseItem) {
    const parts = []
    if (houseItem.buildingNumber) parts.push(houseItem.buildingNumber)
    if (houseItem.unitNumber) parts.push(houseItem.unitNumber)
    if (houseItem.floorNumber) parts.push(houseItem.floorNumber)
    if (houseItem.roomNumber) parts.push(houseItem.roomNumber)
    return parts.join('')
  },

  // 获取角色文本
  getResidentTypeText: function (role) {

    const matchedElement = this.data.residentTypeOptions.find(option => option.nameEn === role);

    if (matchedElement)
      return matchedElement.nameCn
    else
      return role
  },


  // 跳转到房屋详情页
  goToHouseDetail: function (e) {
    const id = e.currentTarget.dataset.id;

    wx.navigateTo({
      url: `./detail/detail?id=${id}`
    });
  },

  // 跳转到添加房屋页面
  goToAddHouse: function () {
    let url = '/profilePackage/pages/profile/house/add/add?mode=add';

    // 如果有来源页面，则传递参数
    if (this.fromPage) {
      url += `&from=${this.fromPage}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  // 编辑房屋
  editHouse: function (e) {
    const id = e.currentTarget.dataset.id;

    // 跳转到编辑页面
    let url = `/profilePackage/pages/profile/house/add/add?mode=edit&id=${id}`;

    // 如果有来源页面，则传递参数
    if (this.fromPage) {
      url += `&from=${this.fromPage}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  // ==================== 新增房屋弹窗相关方法 ====================

  // 显示新增房屋弹窗
  showAddHouseModal: function () {
    this.resetModalData();
    this.setData({
      showAddHouseModal: true
    });
    this.loadBuildings();
  },

  // 隐藏新增房屋弹窗
  hideAddHouseModal: function () {
    this.setData({
      showAddHouseModal: false
    });
    this.resetModalData();
  },

  // 重置弹窗数据
  resetModalData: function () {
    this.setData({
      currentStep: 'building',
      searchKeyword: '',
      searchPlaceholder: '搜索楼栋',
      selectedBuildingId: null,
      selectedBuildingName: '',
      selectedUnitNumber: '',
      selectedRoomId: null,
      selectedRoomName: '',
      selectedResidentType: '',
      buildings: [],
      units: [],
      rooms: [],
      originalRooms: [],
      filteredBuildings: [],
      filteredUnits: [],
      filteredRooms: [],
      canSubmit: false,
      isSubmitting: false
    });
  },

  // 防止弹窗滚动穿透
  preventTouchMove: function () {
    return false;
  },

  // 加载住户身份字典
  loadResidentTypeDict: function () {
    try {
      const residentTypeDict = util.getDictByNameEn('resident_type');
      if (residentTypeDict && residentTypeDict.length > 0 && residentTypeDict[0].children) {
        this.setData({
          residentTypeOptions: residentTypeDict[0].children
        });
      } else {
        console.warn('住户身份字典数据为空');
        this.setData({
          residentTypeOptions: []
        });
      }
    } catch (error) {
      console.error('加载住户身份字典失败:', error);
      this.setData({
        residentTypeOptions: []
      });
    }
  },

  // 加载楼栋列表
  loadBuildings: function () {
    const communityId = wx.getStorageSync('selectedCommunity').id;
    const params = {
      pageNum: 1,
      pageSize: 500,
      communityId: communityId
    };

    houseApi.getBuildingsByCommunity(params)
      .then(res => {
        console.log('楼栋列表数据：', res);
        if (res && res.list) {
          this.setData({
            buildings: res.list,
            filteredBuildings: res.list
          });
        } else {
          wx.showToast({
            title: '获取楼栋列表失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取楼栋列表异常：', err);

      });
  },

  // 加载房间列表
  loadRooms: function (buildingId) {
    houseApi.getRoomsByBuilding(buildingId)
      .then(res => {
        console.log('房间列表数据：', res);
        if (res && res.list) {
          if (res.list.length === 0) {
            // 没有房间数据时的提示
            wx.showModal({
              title: '提示',
              content: '该楼栋下暂无房间信息，请选择其他楼栋或联系物业管理员',
              showCancel: true,
              cancelText: '重新选择',
              confirmText: '确定',
              success: (modalRes) => {
                if (modalRes.confirm || modalRes.cancel) {
                  // 返回楼栋选择
                  this.backToBuilding();
                }
              }
            });
          } else {
            // 保存原始房间数据
            this.setData({
              originalRooms: res.list
            });

            // 分析房间数据，决定显示层级
            this.processRoomData(res.list);
          }
        } else {
          wx.showToast({
            title: '获取房间列表失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取房间列表异常：', err);

      });
  },

  // 处理房间数据，根据unitNumber决定显示层级
  processRoomData: function (roomList) {
    // 检查是否有房间包含unitNumber
    const hasUnits = roomList.some(room => room.unitNumber && room.unitNumber.trim() !== '');

    if (hasUnits) {
      // 有单元信息，需要混合显示单元和房间
      const unitMap = new Map();
      const noUnitRooms = []; // 无单元的房间

      roomList.forEach(room => {
        if (room.unitNumber && room.unitNumber.trim() !== '') {
          // 有单元的房间，按单元分组
          const unitNumber = room.unitNumber;
          if (!unitMap.has(unitNumber)) {
            unitMap.set(unitNumber, []);
          }
          unitMap.get(unitNumber).push(room);
        } else {
          // 无单元的房间，直接放入无单元数组
          noUnitRooms.push(room);
        }
      });

      // 构建混合显示列表：单元 + 无单元房间
      const mixedItems = [];

      // 添加单元项
      Array.from(unitMap.keys()).forEach(unitNumber => {
        mixedItems.push({
          type: 'unit',
          unitNumber: unitNumber,
          displayName: unitNumber,
          rooms: unitMap.get(unitNumber)
        });
      });

      // 添加无单元的房间项
      noUnitRooms.forEach(room => {
        mixedItems.push({
          type: 'room',
          id: room.id,
          roomNumber: room.roomNumber,
          displayName: room.roomNumber,
          roomData: room
        });
      });

      this.setData({
        units: mixedItems,
        filteredUnits: mixedItems,
        currentStep: 'unit',
        searchKeyword: '',
        searchPlaceholder: '搜索单元/房间'
      });
    } else {
      // 没有单元信息，直接显示房间
      this.setData({
        rooms: roomList,
        filteredRooms: roomList,
        currentStep: 'room',
        searchKeyword: '',
        searchPlaceholder: '搜索房间'
      });
    }
  },

  // 搜索输入处理
  onSearchInput: function (e) {
    const keyword = e.detail.value.trim();
    this.setData({
      searchKeyword: keyword
    });

    if (this.data.currentStep === 'building') {
      this.filterBuildings(keyword);
    } else if (this.data.currentStep === 'unit') {
      this.filterUnits(keyword);
    } else if (this.data.currentStep === 'room') {
      this.filterRooms(keyword);
    }
  },

  // 筛选楼栋
  filterBuildings: function (keyword) {
    const buildings = this.data.buildings;
    if (!keyword) {
      this.setData({
        filteredBuildings: buildings
      });
      return;
    }

    const filtered = buildings.filter(building =>
      building.buildingNumber && building.buildingNumber.includes(keyword)
    );

    this.setData({
      filteredBuildings: filtered
    });
  },

  // 筛选单元（支持混合搜索单元和房间）
  filterUnits: function (keyword) {
    const units = this.data.units;
    if (!keyword) {
      this.setData({
        filteredUnits: units
      });
      return;
    }

    const filtered = units.filter(item => {
      if (item.type === 'unit') {
        // 单元项：搜索单元名称
        return item.displayName && item.displayName.includes(keyword);
      } else if (item.type === 'room') {
        // 房间项：搜索房间号
        return item.roomNumber && item.roomNumber.includes(keyword);
      }
      return false;
    });

    this.setData({
      filteredUnits: filtered
    });
  },

  // 筛选房间
  filterRooms: function (keyword) {
    const rooms = this.data.rooms;
    if (!keyword) {
      this.setData({
        filteredRooms: rooms
      });
      return;
    }

    const filtered = rooms.filter(room =>
      room.roomNumber && room.roomNumber.includes(keyword)
    );

    this.setData({
      filteredRooms: filtered
    });
  },

  // 选择楼栋
  selectBuilding: function (e) {
    const buildingId = e.currentTarget.dataset.id;
    const buildingName = e.currentTarget.dataset.name;

    this.setData({
      selectedBuildingId: buildingId,
      selectedBuildingName: buildingName,
      selectedUnitNumber: '',
      selectedRoomId: null,
      selectedRoomName: ''
    });

    this.checkCanSubmit();
    this.loadRooms(buildingId);
  },

  // 选择单元或房间（混合选择）
  selectUnit: function (e) {
    const unitNumber = e.currentTarget.dataset.unit;
    const roomId = e.currentTarget.dataset.roomId;
    const roomName = e.currentTarget.dataset.roomName;

    console.log('选择项目:', { unitNumber, roomId, roomName });

    // 判断是选择单元还是直接选择房间
    if (roomId) {
      // 直接选择房间（无单元的房间）
      console.log('直接选择房间:', roomId, roomName);
      this.setData({
        selectedRoomId: roomId, // 保持字符串类型，与WXML中的比较一致
        selectedRoomName: roomName,
        selectedUnitNumber: '' // 清空单元选择
      });
      this.checkCanSubmit();
    } else if (unitNumber) {
      // 选择单元，进入房间选择
      console.log('选择单元:', unitNumber);
      const unitData = this.data.units.find(unit => unit.unitNumber === unitNumber && unit.type === 'unit');

      if (unitData) {
        this.setData({
          selectedUnitNumber: unitNumber,
          rooms: unitData.rooms,
          filteredRooms: unitData.rooms,
          currentStep: 'room',
          searchKeyword: '',
          searchPlaceholder: '搜索房间',
          selectedRoomId: null,
          selectedRoomName: ''
        });

        this.checkCanSubmit();
      }
    }
  },

  // 返回楼栋选择
  backToBuilding: function () {
    this.setData({
      currentStep: 'building',
      searchKeyword: '',
      searchPlaceholder: '搜索楼栋',
      selectedUnitNumber: '',
      selectedRoomId: null,
      selectedRoomName: '',
      units: [],
      rooms: [],
      filteredUnits: [],
      filteredRooms: []
    });

    this.checkCanSubmit();

    // 重新筛选楼栋列表
    this.filterBuildings('');
  },

  // 返回单元选择
  backToUnit: function () {
    this.setData({
      currentStep: 'unit',
      searchKeyword: '',
      searchPlaceholder: '搜索单元',
      selectedRoomId: null,
      selectedRoomName: '',
      rooms: [],
      filteredRooms: []
    });

    this.checkCanSubmit();

    // 重新筛选单元列表
    this.filterUnits('');
  },

  // 选择房间
  selectRoom: function (e) {
    const roomId = e.currentTarget.dataset.id;
    const roomName = e.currentTarget.dataset.name;

    this.setData({
      selectedRoomId: roomId, // 保持字符串类型
      selectedRoomName: roomName
    });

    this.checkCanSubmit();
  },

  // 选择住户身份
  selectResidentType: function (e) {
    const residentType = e.currentTarget.dataset.type;

    this.setData({
      selectedResidentType: residentType
    });

    this.checkCanSubmit();
  },

  // 检查是否可以提交
  checkCanSubmit: function () {
    const canSubmit = this.data.selectedBuildingId &&
      this.data.selectedRoomId &&
      this.data.selectedResidentType;

    this.setData({
      canSubmit: canSubmit
    });
  },

  // 提交新增房屋
  submitAddHouse: function () {
    if (!this.data.canSubmit || this.data.isSubmitting) {
      return;
    }

    this.setData({
      isSubmitting: true
    });

    const roomData = {
      buildingId: this.data.selectedBuildingId,
      roomId: parseInt(this.data.selectedRoomId), // 提交时转换为数字
      residentType: this.data.selectedResidentType
    };

    console.log('提交房屋数据：', roomData);

    houseApi.addHouse(roomData)
      .then(res => {
        console.log('新增房屋成功：', res);


        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });

        this.hideAddHouseModal();
        // 刷新房产列表
        setTimeout(() => {
          this.loadHouses(true);
        }, 100);

      })
      .catch(err => {
        console.error('新增房屋异常：', err);

      })
      .finally(() => {
        this.setData({
          isSubmitting: false
        });
      });
  },




  // ==================== 查找我的房产功能 ====================

  // 检查实名认证房产
  checkRealPersonHouse: function () {
    console.log('开始检查实名认证房产信息');
    const communityId = wx.getStorageSync('selectedCommunity').id;
    houseApi.getHouseByRealPerson(communityId)
      .then(res => {
        console.log('实名认证房产信息：', res);

        if (res) {
          // 有实名认证房产信息，显示绑定弹窗
          this.setData({
            realPersonData: res
          });
          this.showFindHouseModal();
        } else {
          // 没有实名认证房产信息，显示提示
          wx.showModal({
            title: '提示',
            content: '暂无可绑定的房产信息，请联系物业管理员或手动添加房产',
            showCancel: true,
            cancelText: '手动添加',
            confirmText: '确定',
            success: (modalRes) => {
              if (modalRes.cancel) {
                // 跳转到手动添加房产页面
                this.showAddHouseModal();
              }
            }
          });
        }
      })
      .catch(err => {
        console.error('获取实名认证房产信息异常：', err);

      });
  },

  // 显示查找房产弹窗
  showFindHouseModal: function () {
    this.setData({
      showFindHouseModal: true,
      availableHouses: [],
      selectedHousesCount: 0,
      findHouseLoading: false,
      bindingHouses: false
    });
    this.loadAvailableHouses();
  },

  // 隐藏查找房产弹窗
  hideFindHouseModal: function () {
    // 重置所有相关状态
    this.setData({
      showFindHouseModal: false,
      findHouseLoading: false,
      availableHouses: [],
      selectedHousesCount: 0,
      bindingHouses: false
    });
  },

  // 加载住户房产列表（基于实名认证数据的roomList）
  loadAvailableHouses: function () {
    if (!this.data.realPersonData || !this.data.realPersonData.roomList) {
      console.error('没有实名认证房产数据');
      this.setData({ findHouseLoading: false });
      return;
    }

    this.setData({ findHouseLoading: true });

    // 处理实名认证房产数据（来自roomList）
    const roomList = this.data.realPersonData.roomList.map((item, index) => ({
      id: item.id || `room_${index}`,
      roomId: item.roomId || item.id,
      buildingNumber: item.buildingNumber || '',
      unitNumber: item.unitNumber || '',
      floorNumber: item.floorNumber || '',
      roomNumber: item.roomNumber || '',
      fullAddress: this.formatHouseAddress(item),
      area: item.area || 0,
      residentType: item.residentType || 'owner',
      residentTypeText: this.getResidentTypeText(item.residentType || 'owner'),
      isBind: item.isBind || false // 使用API返回的isBind状态
    }));

    // 更新realPersonData中的roomList
    const updatedRealPersonData = {
      ...this.data.realPersonData,
      roomList: roomList
    };

    this.setData({
      realPersonData: updatedRealPersonData,
      findHouseLoading: false
    });

    console.log('住户房产列表处理完成：', {
      residentId: this.data.realPersonData.id,
      residentName: this.data.realPersonData.residentName,
      total: roomList.length,
      rooms: roomList.map(room => ({
        id: room.id,
        roomId: room.roomId,
        fullAddress: room.fullAddress,
        isBind: room.isBind
      }))
    });
  },

  // 获取状态文本
  getStatusText: function (status) {
    switch (status) {
      case 'approved': return '已绑定';
      case 'pending': return '待绑定';
      case 'rejected': return '已拒绝';
      default: return '待绑定';
    }
  },



  // 绑定住户
  bindResident: function () {
    if (!this.data.realPersonData || !this.data.realPersonData.id) {
      wx.showToast({
        title: '没有住户数据',
        icon: 'none'
      });
      return;
    }

    this.setData({ bindingHouses: true });

    // 使用实名认证数据的ID进行绑定
    const residentId = this.data.realPersonData.id;

    console.log('绑定住户ID：', residentId);
    console.log('住户信息：', {
      id: this.data.realPersonData.id,
      residentName: this.data.realPersonData.residentName,
      phone: this.data.realPersonData.phone,
      roomListCount: this.data.realPersonData.roomList ? this.data.realPersonData.roomList.length : 0
    });

    // 调用绑定接口
    houseApi.bindHouse(residentId)
      .then(res => {
        console.log('绑定住户结果：', res);


        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });

        // 关闭弹窗
        this.hideFindHouseModal();

        // 刷新房产列表
        setTimeout(() => {
          this.loadHouses(true);
        }, 100);


      })
      .catch(err => {
        console.error('绑定住户异常：', err);

        this.setData({ bindingHouses: false });
      });
  },

  // ==================== 页面生命周期方法 ====================

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadHouses(true);
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function () {
    this.loadHouses();
  }

})
