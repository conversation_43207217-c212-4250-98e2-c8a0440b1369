// pages/goods/order/order.js
const util = require('@/utils/util.js')
const dateUtil = require('@/utils/dateUtil.js')
const app = getApp()
const PointsUtil = require('@/utils/points')
const goodsApi = require('@/api/goods.js')
const commApi = require('@/api/commApi.js')
const QR = require('@/utils/qrcode.js')

Page({
  data: {
    darkMode: false,
    orderId: null,
    searchType: null,
    order: null,
    qrCodeVisible: false,
    apiUrl: '', // 图片访问路径
    orderStatusOptions: [], // 订单状态字典
    qrcodeGenerated: false, // 二维码是否已生成
    imagePath: '', // 二维码图片路径
    qrCodeToken: '', // 二维码token
    qrCodeExpireTime: '', // 二维码过期时间
    isGeneratingQRCode: false, // 是否正在生成二维码
    showCancelModal: false, // 是否显示取消原因选择弹窗
    cancelReasons: [ // 取消原因选项（前三个是单选项）
      { value: 'wrong_order', text: '拍错了' },
      { value: 'no_need', text: '不想要了' },
      { value: 'price_issue', text: '价格问题' }
    ],
    selectedCancelReason: '',
    customCancelReason: ''
  },

  onLoad: function (options) {
    // 获取暗黑模式设置
    this.setData({
      darkMode: app.globalData.darkMode,
      apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/'
    });

    // 加载订单状态字典
    this.loadOrderStatusDictionary();

    if (options.id && options.searchType) {
       
      this.setData({
        orderId: options.id,
        searchType: options.searchType
      })
      this.loadOrderDetail(options.id)
    }
  },

  // 加载订单状态字典
  loadOrderStatusDictionary: function () {
    try {
      // 使用统一的字典获取方法
      const orderStatusDict = util.getDictByNameEn('good_stuff_order_status');
      this.setData({
        orderStatusOptions: orderStatusDict && orderStatusDict.length > 0 && orderStatusDict[0].children ? orderStatusDict[0].children : []
      });
      console.log('订单状态字典加载完成:', this.data.orderStatusOptions);
    } catch (error) {
      console.error('加载订单状态字典失败:', error);
      this.setData({
        orderStatusOptions: []
      });
    }
  },

  // 获取订单状态显示名称
  getOrderStatusName: function (status) {
    console.log('获取订单状态名称:', status, '字典选项:', this.data.orderStatusOptions);
    const statusOption = this.data.orderStatusOptions.find(option => option.nameEn === status);
    const statusName = statusOption ? statusOption.nameCn : status;
    console.log('状态名称结果:', statusName);
    return statusName;
  },

  // 检测订单是否超时
  checkOrderExpired: function (expireTime) {
    if (!expireTime) {
      return false; // 没有过期时间，认为未超时
    }

    try {
      const now = new Date();
      const expireDate = new Date(expireTime);

      console.log('检测订单超时:', {
        now: now.toISOString(),
        expireTime: expireTime,
        expireDate: expireDate.toISOString(),
        isExpired: now > expireDate
      });

      return now > expireDate;
    } catch (error) {
      console.error('解析过期时间失败:', error);
      return false;
    }
  },

  // 加载订单详情
  loadOrderDetail: function (orderId) {
    wx.showLoading({
      title: '加载中...'
    });

    // 使用真实API获取订单详情
    goodsApi.getMyGoodsOrderDetail(orderId).then(res => {
      if (res) {
        const orderData = res;

        // 解析 stuffSnapshot 获取商品信息
        let goodsInfo = {};
        if (orderData.stuffSnapshot) {
          try {
            goodsInfo = JSON.parse(orderData.stuffSnapshot);
          } catch (e) {
            console.error('解析 stuffSnapshot 失败:', e);
            goodsInfo = {};
          }
        }

        // 检测订单是否超时
        const isExpired = this.checkOrderExpired(orderData.expireTime);

        // 处理订单数据
        const order = {
          id: orderData.id,
          orderNo: orderData.orderNo || '',
          goodsId: goodsInfo.id || '',
          stuffDescribe: goodsInfo.stuffDescribe || '',
          amount: orderData.unitAmount || goodsInfo.amount || 0,
          totalAmount: orderData.totalAmount || 0,
          unitAmount: orderData.unitAmount || 0,
          quantity: orderData.quantity || 1,
          status: orderData.status || 'pending',
          statusName: this.getOrderStatusName(orderData.status),
          createTime: orderData.createTime ? dateUtil.formatTime(new Date(orderData.createTime)) : '',
          updateTime: orderData.updateTime ? dateUtil.formatTime(new Date(orderData.updateTime)) : '',
          sellerName: goodsInfo.userName || '卖家',
          sellerId: orderData.sellerId || goodsInfo.userId,
          phone: orderData.phone || '',
          note: orderData.note || '',
          address: goodsInfo.address || '',
          expireTime: orderData.expireTime,
          isExpired: isExpired,
          // 商品图片
          image: goodsInfo.media ? (this.data.apiUrl + goodsInfo.media.split(',')[0]) : '',
          // 保存原始快照数据
          stuffSnapshot: goodsInfo
        };

        console.log('设置订单数据:', order);
        this.setData({
          order: order
        });

        // 如果订单状态不是已完成且未超时，生成二维码
        if (this.data.searchType == 'buyer' && order.status !== 'complete' && order.status !== 'cancel' && !order.isExpired) {

          this.getVerifyCode(orderId)
          // this.generateQrcode();


        }

        wx.hideLoading();
        console.log('订单详情加载完成');
      } else {
        throw new Error(res.message || '获取订单详情失败');
      }
    }).catch(err => {
      console.error('获取订单详情失败:', err);
      wx.hideLoading();

      // 设置一个空的订单对象，避免一直显示加载中
      this.setData({
        order: {
          id: '',
          orderNo: '加载失败',
          stuffDescribe: '订单信息加载失败',
          status: 'error',
          statusName: '加载失败'
        }
      });

      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },


  getVerifyCode(orderId) {
    console.log('开始获取核销码:', orderId);

    // 设置生成状态
    this.setData({
      isGeneratingQRCode: true,
      qrcodeGenerated: false,
      imagePath: '',
      qrCodeToken: '',
      qrCodeExpireTime: ''
    });

    goodsApi.getVerifyCode(orderId).then(res => {
      console.log('getVerifyCode API返回:', res);

      if ( res) {
        const { token, expireIn } = res;

        // 计算过期时间
        const expireTime = new Date(Date.now() + expireIn * 1000);
        const formattedExpireTime = dateUtil.formatDateTime(expireTime);

        // 保存token和过期时间
        this.setData({
          qrCodeToken: token,
          qrCodeExpireTime: formattedExpireTime
        });

        // 生成二维码内容
        const qrContent = `order:${this.data.order.id}:${token}`;
        console.log('生成订单核销二维码内容:', qrContent);

        // 设置画布大小并生成二维码
        const size = this.setCanvasSize();
        this.createQrCode(qrContent, "orderCanvas", size.w, size.h);
      } else {
        this.setData({
          isGeneratingQRCode: false
        });
        wx.showToast({
          title: res.errorMessage || '获取核销码失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取订单码失败:', err);
      this.setData({
        isGeneratingQRCode: false
      });
      wx.showToast({
        title: '获取订单码失败',
        icon: 'none'
      });
    });
  },



  // 设置画布大小
  setCanvasSize: function () {
    const size = {};
    try {
      const res = wx.getSystemInfoSync();
      const scale = 750 / res.windowWidth;
      const width = 380 / scale;
      const height = 380 / scale;
      size.w = width;
      size.h = height;
    } catch (e) {
      console.log("获取设备信息失败" + e);
      size.w = 380;
      size.h = 380;
    }
    return size;
  },

  // 创建二维码
  createQrCode: function (url, canvasId, cavW, cavH) {
    console.log('开始创建订单核销二维码:', {
      url: url,
      canvasId: canvasId,
      width: cavW,
      height: cavH
    });

    const that = this;

    // 调用插件中的draw方法，绘制二维码图片
    setTimeout(() => {
      try {
        QR.api.draw(url, canvasId, cavW, cavH, this, function() {
          console.log('订单核销二维码绘制完成，开始转换为图片');
          that.canvasToTempImage();
        });
      } catch (error) {
        console.error('订单核销二维码生成失败:', error);
        that.setData({
          isGeneratingQRCode: false
        });
        wx.showToast({
          title: '二维码生成失败',
          icon: 'none'
        });
      }
    }, 300);
  },

  // 获取临时缓存照片路径，存入data中
  canvasToTempImage: function () {
    const that = this;
    wx.canvasToTempFilePath({
      canvasId: 'orderCanvas',
      x: 0,
      y: 0,
      width: 300,
      height: 300,
      destWidth: 300,
      destHeight: 300,
      success: function (res) {
        const tempFilePath = res.tempFilePath;
        console.log('订单核销二维码生成成功:', tempFilePath);
        that.setData({
          imagePath: tempFilePath,
          qrcodeGenerated: true,
          isGeneratingQRCode: false
        });
      },
      fail: function (res) {
        console.log('生成订单核销二维码图片失败:', res);
        wx.showToast({
          title: '二维码生成失败',
          icon: 'none'
        });
        that.setData({
          isGeneratingQRCode: false
        });
      }
    }, this);
  },

  // 显示取消订单弹窗
  showCancelModal: function () {
    this.setData({
      showCancelModal: true,
      selectedCancelReason: '',
      customCancelReason: ''
    });
  },

  // 隐藏取消订单弹窗
  hideCancelModal: function () {
    this.setData({
      showCancelModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 空方法，用于阻止事件冒泡
  },

  // 选择取消原因
  selectCancelReason: function (e) {
    const reason = e.currentTarget.dataset.reason;
    console.log('选择预设原因:', reason);
    this.setData({
      selectedCancelReason: reason,
      customCancelReason: '' // 选择预设原因时清空自定义输入
    });
    console.log('当前选择的原因:', this.data.selectedCancelReason);
  },

  // 选择其他原因
  selectOtherReason: function () {
    console.log('选择其他原因');
    this.setData({
      selectedCancelReason: 'other'
    });
    console.log('当前选择的原因:', this.data.selectedCancelReason);
  },

  // 输入自定义取消原因
  inputCustomReason: function (e) {
    this.setData({
      customCancelReason: e.detail.value
    });
  },

  // 确认取消订单
  confirmCancelOrder: function () {
    if (!this.data.selectedCancelReason) {
      wx.showToast({
        title: '请选择取消原因',
        icon: 'none'
      });
      return;
    }

    if (this.data.selectedCancelReason === 'other' && !this.data.customCancelReason.trim()) {
      wx.showToast({
        title: '请输入取消原因',
        icon: 'none'
      });
      return;
    }

    // 获取取消原因文本
    let cancelNote = '';
    if (this.data.selectedCancelReason === 'other') {
      cancelNote = this.data.customCancelReason.trim();
    } else {
      const reasonObj = this.data.cancelReasons.find(r => r.value === this.data.selectedCancelReason);
      cancelNote = reasonObj ? reasonObj.text : '用户取消';
    }

    this.hideCancelModal();
    this.performCancelOrder(cancelNote);
  },

  // 执行取消订单操作
  performCancelOrder: function (note) {
    wx.showLoading({
      title: '处理中...'
    });

    // 调用取消订单API
    goodsApi.cancelMyGoodsOrder({
      id: this.data.order.id,
      note: note
    }).then(res => {
      wx.hideLoading();
    
        this.setData({
          'order.status': 'cancel',
          'order.statusName': this.getOrderStatusName('cancel'),
          'order.updateTime': dateUtil.formatTime(new Date()),
          'order.note': note
        });

        wx.showToast({
          title: '取消成功',
          icon: 'success'
        });
 
    }).catch(err => {
      wx.hideLoading();
      console.error('取消订单失败:', err);
      wx.showToast({
        title: err.message || '取消失败',
        icon: 'none'
      });
    });
  },





  // 取消订单
  cancelOrder: function () {
    // 显示取消原因选择弹窗
    this.showCancelModal();
  },

  // 联系卖家
  contactSeller: function () {
    const order = this.data.order;
    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none'
      });
      return;
    }

    // 直接跳转到聊天页面
    wx.navigateTo({
      url: `/pages/messages/chat?targetId=${order.sellerId}&targetName=${order.sellerName}&goodsId=${order.goodsId}&goodsTitle=${encodeURIComponent(order.stuffDescribe)}&goodsImage=${encodeURIComponent(order.image)}`
    });
  },

  // 导航到商品详情
  navigateToGoodsDetail: function () {
    const order = this.data.order;
    if (order && order.goodsId) {
      wx.navigateTo({
        url: `/profilePackage/pages/goods/detail/detail?id=${order.goodsId}`
      });
    } else {
      wx.showToast({
        title: '商品信息不存在',
        icon: 'none'
      });
    }
  }
})
