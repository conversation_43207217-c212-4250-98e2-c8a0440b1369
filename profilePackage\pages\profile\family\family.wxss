/* 我的家人页面样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
}



/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: auto;
  max-height: calc(100vh - 50px); /* 减去底部按钮区域的高度 */
}

/* 家人列表样式 */
.family-list {
  margin-bottom: 80px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #8E8E93;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23C7C7CC' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 00-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 010 7.75'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 10px;
}

.empty-subtext {
  font-size: 14px;
  opacity: 0.7;
}

/* 家人卡片样式 */
.family-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  position: relative;
}

.family-card-content {
  display: flex;
  padding: 16px;
  position: relative;
}

.family-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f0f0f0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
  color: #FF9500;
  font-size: 24px;
  font-weight: 600;
}

.family-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.family-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.family-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #333;
}

.family-relation-container {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.family-relation {
  display: inline-block;
  padding: 3px 10px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
  background-color: #E1F0FF;
  color: #007AFF;
  width: auto;
  min-width: 40px;
  text-align: center;
  margin-bottom: 0;
  line-height: 1.2;
}

.relation-parent {
  background-color: #E1F0FF;
  color: #007AFF;
}

.relation-child {
  background-color: #E6FFF2;
  color: #34C759;
}

.relation-spouse {
  background-color: #FFE8E6;
  color: #FF3B30;
}

.relation-sibling {
  background-color: #F2E8FF;
  color: #AF52DE;
}

.relation-grandparent {
  background-color: #E6F9FF;
  color: #5AC8FA;
}

.family-house {
  font-size: 14px;
  color: #8E8E93;
  display: flex;
  align-items: center;
}

.house-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'%3E%3C/path%3E%3Cpath d='M9 22V12h6v10'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.family-actions {
  display: flex;
  align-items: center;
}

.action-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  background: transparent;
}

.more-icon {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='1'%3E%3C/circle%3E%3Ccircle cx='19' cy='12' r='1'%3E%3C/circle%3E%3Ccircle cx='5' cy='12' r='1'%3E%3C/circle%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 添加按钮 */
.add-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.add-button {
  width: 100%;
  height: 50px;
  background: #FF9500;
  color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
  box-shadow: 0 4px 12px rgba(255, 149, 0, 0.2);
}

.add-button::before {
  content: '+';
  margin-right: 8px;
  font-size: 20px;
}

/* 操作菜单样式 */
.action-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}

.action-menu {
  position: fixed;
  background: white;
  border-radius: 14px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 100;
  width: 180px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.05);
  /* 确保菜单不会超出屏幕右侧 */
  right: 20px;
}

.action-menu-item {
  padding: 14px 16px;
  font-size: 16px;
  color: #333;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.action-menu-item:last-child {
  border-bottom: none;
}

.action-menu-item.delete {
  color: #FF3B30;
}

.edit-icon {
  width: 18px;
  height: 18px;
  margin-right: 10px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.delete-icon {
  width: 18px;
  height: 18px;
  margin-right: 10px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF3B30' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2'%3E%3C/path%3E%3Cline x1='10' y1='11' x2='10' y2='17'%3E%3C/line%3E%3Cline x1='14' y1='11' x2='14' y2='17'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 200;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-overlay.active {
  display: block;
  opacity: 1;
}

.modal-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 80%;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  z-index: 201;
}

.modal-overlay.active .modal-container {
  transform: translateY(0);
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-actions {
  display: flex;
  align-items: center;
}

.modal-sync {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FF9500;
  background: transparent;
  margin-right: 8px;
}

.sync-icon {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF9500' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.12 0 4.07.74 5.62 1.98'%3E%3C/path%3E%3Cpath d='M20 4v5h-5'%3E%3C/path%3E%3Cpath d='M15 9l5-5'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.modal-close {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  background: transparent;
}

.close-icon {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.modal-body {
  padding: 20px;
  max-height: calc(80vh - 130px);
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.required {
  color: #FF3B30;
  margin-left: 4px;
}

.form-input {
  width: 100%;
  height: 44px;
  border: 1px solid #E5E5EA;
  border-radius: 10px;
  background-color: #F9F9F9;
  padding: 0 12px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input input {
  width: 100%;
  height: 100%;
  font-size: 16px;
  color: #333;
  background: transparent;
}

.form-selector {
  width: 100%;
  height: 44px;
  border: 1px solid #E5E5EA;
  border-radius: 10px;
  background-color: #F9F9F9;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

/* 下拉菜单样式 */
.form-dropdown {
  width: 100%;
  position: relative;
}

.dropdown-header {
  width: 100%;
  height: 44px;
  border: 1px solid #E5E5EA;
  border-radius: 10px;
  background-color: #F9F9F9;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border: 1px solid #E5E5EA;
  border-radius: 10px;
  margin-top: 4px;
  max-height: 0;
  overflow-y: auto;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.dropdown-content.show {
  max-height: 300px;
  opacity: 1;
  visibility: visible;
}

.dropdown-group {
  border-bottom: 1px solid #F2F2F7;
  padding: 8px 0;
}

.dropdown-group:last-child {
  border-bottom: none;
}

.dropdown-group-title {
  font-size: 12px;
  color: #8E8E93;
  padding: 4px 16px;
}

.dropdown-item {
  padding: 12px 16px;
  font-size: 16px;
  color: #333;
  cursor: pointer;
}

.dropdown-item:hover, .dropdown-item.active {
  background-color: #F2F2F7;
  color: #FF9500;
}

.arrow-icon.up {
  transform: rotate(180deg);
}

.placeholder {
  color: #C7C7CC;
}

.arrow-icon {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 导入样式 */
.import-container {
  padding: 10px 0;
}

.import-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.spinner {
  width: 36px;
  height: 36px;
  border: 3px solid rgba(255, 149, 0, 0.2);
  border-top-color: #FF9500;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.import-text {
  font-size: 14px;
  color: #8E8E93;
  text-align: center;
  line-height: 1.5;
}

.import-text-secondary {
  font-size: 12px;
  color: #AEAEB2;
  margin-top: 8px;
  text-align: center;
}

.import-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.import-tip {
  font-size: 14px;
  color: #8e8e93;
  margin-bottom: 12px;
  font-style: italic;
}

.import-list {
  max-height: 300px;
  overflow-y: auto;
  border-radius: 12px;
  background: #f9f9f9;
}

.import-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #E5E5EA;
}

.import-item:last-child {
  border-bottom: none;
}

.import-item-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #FF9500;
  font-size: 16px;
  font-weight: 600;
}

.import-item-info {
  flex: 1;
}

.import-item-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #333;
}

.import-item-phone {
  font-size: 14px;
  color: #8E8E93;
}

.import-item-checkbox {
  width: 22px;
  height: 22px;
  border-radius: 4px;
  border: 2px solid #C7C7CC;
  position: relative;
  transition: all 0.2s ease;
}

.import-item-checkbox.checked {
  background-color: #FF9500;
  border-color: #FF9500;
}

.import-item-checkbox.checked::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 6px;
  width: 6px;
  height: 10px;
  border-right: 2px solid white;
  border-bottom: 2px solid white;
  transform: rotate(45deg);
}

/* 按钮样式 */
.cancel-button,
.submit-button {
  flex: 1;
  height: 50px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-button {
  background-color: #F2F2F7;
  color: #8E8E93;
}

.submit-button {
  background-color: #FF9500;
  color: white;
  box-shadow: 0 4px 12px rgba(255, 149, 0, 0.2);
}

.submit-button[disabled] {
  background-color: #C7C7CC;
  box-shadow: none;
}

/* 选择器样式 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 300;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.picker-overlay.active {
  display: block;
  opacity: 1;
}

.picker-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 301;
}

.picker-container.active {
  transform: translateY(0);
}

.picker-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.picker-close {
  font-size: 16px;
  color: #FF9500;
  font-weight: 500;
}

.picker-content {
  padding: 20px;
}

.picker-item {
  line-height: 40px;
  text-align: center;
  font-size: 16px;
}

.default-house {
  color: #FF9500;
  font-weight: 500;
}

/* 确认删除弹窗样式 */
.alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 999;
  opacity: 0;
  transition: opacity 0.3s;
  display: none;
}

.alert-overlay.show {
  opacity: 1;
  display: block;
}

.custom-alert {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 270px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 14px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  text-align: center;
  display: flex;
  flex-direction: column;
  display: none;
}

.custom-alert.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  display: flex;
}

.alert-content {
  padding: 20px 16px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
  margin-bottom: 8px;
}

.alert-message {
  font-size: 13px;
  color: #8E8E93;
  margin-bottom: 0;
  line-height: 1.4;
}

.alert-button-container {
  width: 100%;
  border-top: 1px solid rgba(60, 60, 67, 0.1);
  display: flex;
}

.alert-button {
  padding: 16px 0;
  font-size: 17px;
  font-weight: 500;
  background: transparent;
  border: none;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s;
}

.alert-cancel-button {
  border-right: 1px solid rgba(60, 60, 67, 0.1);
  color: #FF9500;
  flex: 1;
}

.alert-confirm-button {
  color: #FF3B30;
  font-weight: 600;
  flex: 1;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.loading-text {
  font-size: 14px;
  color: #8e8e93;
  margin-top: 12px;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

/* 家属关系样式 */
.relation-family {
  background-color: #E8F5E8;
  color: #34C759;
}

/* ==================== 邀请二维码弹窗样式 ==================== */

/* 二维码图标 */
.qrcode-icon {
  width: 18px;
  height: 18px;
  margin-right: 10px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='5' height='5'%3E%3C/rect%3E%3Crect x='16' y='3' width='5' height='5'%3E%3C/rect%3E%3Crect x='3' y='16' width='5' height='5'%3E%3C/rect%3E%3Cpath d='M21 16h-3a2 2 0 00-2 2v3'%3E%3C/path%3E%3Cpath d='M21 21v.01'%3E%3C/path%3E%3Cpath d='M12 7v3a2 2 0 002 2h3'%3E%3C/path%3E%3Cpath d='M3 12h.01'%3E%3C/path%3E%3Cpath d='M12 3h.01'%3E%3C/path%3E%3Cpath d='M12 16v.01'%3E%3C/path%3E%3Cpath d='M16 12h1'%3E%3C/path%3E%3Cpath d='M21 12v.01'%3E%3C/path%3E%3Cpath d='M12 21v-1'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 二维码弹窗遮罩和容器 */
.qrcode-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.qrcode-modal.show {
  opacity: 1;
  visibility: visible;
}

.qrcode-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.qrcode-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 400px;
  background-color: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 弹窗头部 */
.qrcode-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.qrcode-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.qrcode-modal-close {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #fff;
  border-radius: 15px;
  transition: background-color 0.2s;
}

.qrcode-modal-close:active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 房间信息区域 */
.qrcode-house-info {
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
}

.qrcode-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.qrcode-info-row:last-child {
  margin-bottom: 0;
}

.qrcode-info-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.qrcode-info-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

/* 二维码显示区域 */
.qrcode-display-area {
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.qrcode-container {
  width: 200px;
  height: 200px;
  background-color: #fff;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.qrcode-image {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  display: block;
  margin: 0 auto;
  object-fit: contain;
  object-position: center;
}

/* 加载状态 */
.qrcode-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f0f0f0;
  border-top: 3px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 未生成状态 */
.qrcode-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.qrcode-placeholder-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.qrcode-placeholder-text {
  font-size: 14px;
  color: #999;
  text-align: center;
  padding: 0 20px;
}

/* 过期时间显示 */
.qrcode-expire-time {
  margin-top: 16px;
  text-align: center;
  padding: 8px 16px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  width: 100%;
  max-width: 300px;
  box-sizing: border-box;
}

.expire-label {
  font-size: 12px;
  color: #856404;
}

.expire-time {
  font-size: 12px;
  color: #856404;
  font-weight: 600;
}

/* 隐藏的canvas */
.qrcode-canvas-hidden {
  position: absolute;
  left: -9999px;
  top: -9999px;
  width: 300px;
  height: 300px;
  opacity: 0;
}

/* 操作按钮区域 */
.qrcode-actions {
  display: flex;
  padding: 20px;
  gap: 12px;
  background-color: #f8f9fa;
}

.qrcode-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

.qrcode-btn-secondary:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
}

.qrcode-btn-secondary:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  transform: none;
}

.qrcode-btn-primary {
  background: linear-gradient(135deg, #007AFF, #0056b3);
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.qrcode-btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 122, 255, 0.2);
}

.qrcode-btn-primary:disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
  transform: none;
}
