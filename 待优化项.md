优化我的房产页
用户进入我的房产页,先获取我的房产列表
1.如果列表  res.code==0&& res.data.list.length>0,正常显示列表
2.如果列表  res.code==0&& res.data.list &&res.data.list.length==0,则查询houseApi.getHouseByRealPerson
返回结构是
res:
{
  "errorMessage": null,
  "code": 0,
  "data": {
    "id": "35",
    "residentName": "卜凡傲",
    "birthday": "1990-06-06",
    "gender": "man",
    "certificateType": "id_card",
    "idCardNumber": "320323199006060298",
    "nativePlace": null,
    "phone": "13685156020",
    "tags": "elderly,low_income,party_member,military,grid_member,vip,owner",
    "note": "",
    "createTime": "2025-06-13 19:36:11",
    "updateTime": "2025-06-13 20:56:52",
    "communityId": "2",
    "roomList": [
      {
        "id": "53",
        "residentName": "卜凡傲",
        "phone": "13685156020",
        "buildingId": "4",
        "buildingNumber": "1栋",
        "unitNumber": "1单元",
        "residentId": "35",
        "certificateType": "id_card",
        "idCardNumber": "320323199006060298",
        "roomId": "3",
        "roomNumber": "101",
        "status": "refuse",
        "residentType": "owner",
        "isDefault": false
      },
      {
        "id": "54",
        "residentName": "卜凡傲",
        "phone": "13685156020",
        "buildingId": "4",
        "buildingNumber": "1栋",
        "unitNumber": "1单元",
        "residentId": "35",
        "certificateType": "id_card",
        "idCardNumber": "320323199006060298",
        "roomId": "12",
        "roomNumber": "102",
        "status": "refuse",
        "residentType": "owner",
        "isDefault": false
      },
      {
        "id": "55",
        "residentName": "卜凡傲",
        "phone": "13685156020",
        "buildingId": "5",
        "buildingNumber": "2栋",
        "unitNumber": null,
        "residentId": "35",
        "certificateType": "id_card",
        "idCardNumber": "320323199006060298",
        "roomId": "5",
        "roomNumber": "别墅1号",
        "status": "refuse",
        "residentType": "owner",
        "isDefault": false
      }
    ]
  }
}
如果houseApi.getHouseByRealPerson返回结果res.code==0&& res.data,则显示弹窗.
弹窗顶部是住户信息.住户信息下面是住户的房产列表信息.列表可滚动.
弹窗的底部是取消和绑定按钮.点击取消,收起弹窗.点击绑定,执行houseApi.bindHouse(res.data.id),检测接口返程成功.收起弹窗,刷新我的房产列表.检测接口返回失败,提示绑定住户失败.

点击我的房产列表页右上角 查找我的房产按钮,弹出如上要求弹窗.
