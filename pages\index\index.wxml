<!--index.wxml-->
<!--骨架屏组件-->
<import src="index.skeleton.wxml"/>
<template is="skeleton" wx-if="{{false}}" ></template>

<!-- 使用SVG图标组件 -->
<view wx-if="{{true}}" class="container page-bottom-safe-area {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 顶部渐变背景 -->
  <view class="header-bg"></view>

  <!-- 欢迎区域 -->
  <view class="welcome-section">
    <!-- 小区选择器和用户问候 -->
    <view class="welcome-header">
      <view class="community-selector" bindtap="navigateToCommunitySelect">
        <text class="community-name">{{selectedCommunity || '选择小区'}}</text>
        <text class="community-arrow">▼</text>
      </view>
      <!-- <view class="scan-code-btn" bindtap="scanCode">
        <image src="../../images/icons/scan-code.svg" mode="aspectFit" class="scan-code-icon"></image>
      </view> -->
      <view class="welcome-user">
        <!-- <text class="welcome-text">你好，{{isAuthenticated ? userName : '游客'}}</text> -->
        <image src="../../images/icons/scan-code.svg" mode="aspectFit" class="scan-code-icon" bind:tap="navigateToVerification"></image>
      </view>
    </view>

    <!-- 天气和问候语 -->
    <view class="weather-mood">
      <view class="weather-display">
        <text class="weather-temp">{{weather.temperature}}</text>
        <view class="weather-icon">
          <block wx:if="{{weather.condition === 'sunny'}}">
            <view class="icon-weather-sunny"></view>
          </block>
          <block wx:elif="{{weather.condition === 'cloudy'}}">
            <view class="icon-weather-cloudy"></view>
          </block>
          <block wx:elif="{{weather.condition === 'rainy'}}">
            <view class="icon-weather-rainy"></view>
          </block>
          <block wx:elif="{{weather.condition === 'snowy'}}">
            <view class="icon-weather-snowy"></view>
          </block>
        </view>
      </view>
      <text class="mood-message">{{greeting}}，{{weatherMood}}</text>
    </view>
  </view>

  <!-- 主要功能区 - 轮播Banner -->
  <view class="banner-carousel">
    <!-- 加载状态 -->
    <view class="banner-loading" wx:if="{{bannerLoading}}">
      <view class="loading-text">正在加载轮播图...</view>
    </view>

    <!-- 轮播图 -->
    <swiper class="banner-swiper" wx:if="{{!bannerLoading && bannerList.length > 0}}" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}">
      <swiper-item wx:for="{{bannerList}}" wx:key="id">
        <image src="{{item.imageUrl}}" class="banner-image" mode="aspectFill" bindtap="navigateToBanner" data-url="{{item.linkUrl}}" data-id="{{item.id}}" lazy-load="{{true}}"></image>
        <!-- 轮播图标题（可选） -->
        <view class="banner-title" wx:if="{{item.title}}">{{item.title}}</view>
      </swiper-item>
    </swiper>

    <!-- 无数据状态 -->
    <view class="banner-empty" wx:if="{{!bannerLoading && bannerList.length === 0}}">
      <view class="empty-text">暂无轮播图</view>
    </view>
  </view>

  <!-- 快捷服务 -->
  <view class="card">
    <view class="section-title">
      快捷服务
      <view class="more">全部</view>
    </view>
    <view class="quick-services">
      <view class="service-item" bindtap="navigateToPayment">
        <image class="service-icon" src="../../images/home/<USER>" mode="aspectFit"></image>
        <view class="service-name">物业缴费</view>
      </view>
      <view class="service-item" bindtap="navigateWithAuth" data-url="/servicePackage/pages/repair/repair">
        <image class="service-icon" src="../../images/home/<USER>" mode="aspectFit"></image>
        <view class="service-name">报事报修</view>
      </view>
      <view class="service-item" bindtap="navigateWithAuth" data-url="/servicePackage/pages/access/access">
        <image class="service-icon" src="../../images/home/<USER>" mode="aspectFit"></image>
        <view class="service-name">智慧通行</view>
      </view>
      <view class="service-item" bindtap="navigateToPropertyManagement">
        <image class="service-icon" src="../../images/home/<USER>" mode="aspectFit"></image>
        <view class="service-name">物业管理</view>
      </view>
      <view class="service-item" bindtap="navigateToVisitor">
        <image class="service-icon" src="../../images/home/<USER>" mode="aspectFit"></image>
        <view class="service-name">访客登记</view>
        <view class="visitor-badge" wx:if="{{todayVisitorsCount > 0}}">{{todayVisitorsCount}}</view>
      </view>
      <view class="service-item" bindtap="navigateWithAuth" data-url="/servicePackage/pages/recycle/green/index">
        <image class="service-icon" src="../../images/home/<USER>" mode="aspectFit"></image>
        <view class="service-name">绿色循环</view>
      </view>
      <view class="service-item" bindtap="navigateWithAuth" data-url="/communityPackage/pages/community/service/index">
        <image class="service-icon" src="../../images/home/<USER>" mode="aspectFit"></image>
        <view class="service-name">便民服务</view>
      </view>
      <view class="service-item" bindtap="navigateWithAuth" data-url="/servicePackage/pages/renovation/index/index">
        <image class="service-icon" src="../../images/home/<USER>" mode="aspectFit"></image>
        <view class="service-name">装修报备</view>
      </view>
    </view>
  </view>

  <!-- 社区活动 -->
  <view class="section-title">
    社区活动
    <view class="more" bindtap="navigateToCommunity">更多</view>
  </view>
  <view class="community-events">
    <view class="event-card" wx:for="{{events}}" wx:key="id" bindtap="navigateToEventDetail" data-event="{{item}}">
      <view class="event-image" style="background-image: url('{{item.image}}');"></view>
      <view class="event-content">
        <view class="event-title">{{item.title}}</view>
        <view class="event-description">{{item.description}}</view>
        <view class="event-info">
          <view class="event-info-item">
            <view class="icon-calendar-event"></view>
            <text>{{item.date}}</text>
          </view>
          <view class="event-info-item">
            <view class="icon-people-event"></view>
            <text>已报名: {{item.participants}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>


</view>

<!-- 消息详情弹窗 -->
<view class="message-detail-modal {{showMessageModal ? 'show' : ''}} {{darkMode ? 'darkMode' : ''}}" bindtap="closeMessageModal" data-darkmode="{{darkMode}}">
  <view class="message-detail-content {{darkMode ? 'darkMode' : ''}}" catchtap="stopPropagation" data-darkmode="{{darkMode}}">
    <view class="message-detail-header">
      <view class="message-detail-icon">
        <view class="icon-megaphone"></view>
      </view>
      <view class="message-detail-title-container">
        <view class="message-detail-title">{{currentMessage.title}}</view>
        <view class="message-detail-time">{{currentMessage.time}}</view>
      </view>
    </view>
    <view class="message-detail-body">
      <view class="message-detail-text">{{currentMessage.content}}</view>
    </view>
    <view class="message-detail-footer">
      <button class="message-detail-close-btn" bindtap="closeMessageModal">我知道了</button>
    </view>
  </view>
</view>

<!-- 调试功能区域 (仅开发环境显示) -->
<view wx:if="{{apiUrl && apiUrl.includes('10.37.13.5')}}" class="debug-panel">
  <view class="debug-title">🔧 调试功能</view>
  <view class="debug-buttons">
    <button class="debug-btn" bindtap="debugReloadBanner" size="mini">重载轮播图</button>
    <button class="debug-btn" bindtap="debugTestDefaultBanner" size="mini">测试默认图</button>
    <button class="debug-btn" bindtap="debugCheckStatus" size="mini">检查状态</button>
    <button class="debug-btn" bindtap="debugClearLoginAndReload" size="mini">重新登录</button>
  </view>
</view>

<!-- 物业管理登录弹窗 -->
<view class="property-auth-modal {{showAuthModal ? 'show' : ''}}"   bindtap="closeAuthModal">
  <view class="property-auth-content" catchtap="stopPropagation">
    <view class="modal-title">物业管理登录</view>
    <view class="input-group">
      <input
        class="modal-input {{showAuthModal ? 'show' : ''}}"
        placeholder="{{showAuthModal ? '请输入账号' : ''}}"
        bindinput="onAccountInput"
        value="{{account}}"
        type="text"
        maxlength="50"
        auto-focus="{{false}}"
        adjust-position="{{false}}"
        hold-keyboard="{{false}}"
        confirm-type="next"
        placeholder-style="color: #AEAEB2; opacity: 1;"
        cursor-spacing="0"
        selection-start="-1"
        selection-end="-1"
      />
      <view class="input-clear" wx:if="{{account}}" bindtap="clearAccount">×</view>
    </view>
    <view class="input-group">
      <input
        class="modal-input {{showAuthModal ? 'show' : ''}}"
        placeholder="{{showAuthModal ? '请输入密码' : ''}}"
        password="{{!showPassword}}"
        bindinput="onPasswordInput"
        value="{{password}}"
        type="text"
        maxlength="50"
        auto-focus="{{false}}"
        adjust-position="{{false}}"
        hold-keyboard="{{false}}"
        confirm-type="done"
        placeholder-style="color: #AEAEB2; opacity: 1;"
        cursor-spacing="0"
        selection-start="-1"
        selection-end="-1"
      />
      <view class="input-action" bindtap="togglePasswordVisibility">
        <view class="{{showPassword ? 'icon-eye' : 'icon-eye-off'}}"></view>
      </view>
    </view>
    <!-- 验证码组件已移除 -->
    <view class="remember-account">
      <view class="checkbox {{rememberAccount ? 'checked' : ''}}" bindtap="toggleRememberAccount">
        <view class="checkbox-inner"></view>
      </view>
      <text bindtap="toggleRememberAccount">记住账号密码</text>
    </view>
    <view class="modal-buttons">
      <button class="modal-button cancel-button" bindtap="closeAuthModal">取消</button>
      <button class="modal-button confirm-button" bindtap="confirmAuth">登录</button>
    </view>
  </view>
</view>
